{"folders": [{"path": ".."}, {"path": "E:/文档资料/嵌入式笔记/0 - 项目开发/基于Can进行bootloader升级"}], "settings": {"files.autoGuessEncoding": true, "C_Cpp.default.configurationProvider": "cl.eide", "C_Cpp.errorSquiggles": "disabled", "files.associations": {".eideignore": "ignore", "*.a51": "a51", "*.h": "c", "*.c": "c", "*.hxx": "cpp", "*.hpp": "cpp", "*.c++": "cpp", "*.cpp": "cpp", "*.cxx": "cpp", "*.cc": "cpp"}, "[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 4, "editor.autoIndent": "advanced"}}, "extensions": {"recommendations": ["cl.eide", "keroc.hex-fmt", "xiaoyongdong.srecord", "hars.cppsnippets", "zixuanwang.linkerscript", "redhat.vscode-yaml", "IBM.output-colorizer", "cschlosser.doxdocgen", "ms-vscode.vscode-serial-monitor", "alefragnani.project-manager", "dan-c-underwood.arm", "marus25.cortex-debug"]}}